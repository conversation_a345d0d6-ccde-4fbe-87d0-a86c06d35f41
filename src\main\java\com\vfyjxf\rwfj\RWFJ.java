package com.vfyjxf.rwfj;

import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.fml.ModContainer;
import net.minecraftforge.fml.common.Mod;
import org.slf4j.Logger;

import com.mojang.logging.LogUtils;

import static net.minecraftforge.fml.loading.FMLEnvironment.dist;

@OnlyIn(Dist.CLIENT)
@Mod(value = RWFJ.MODID)
public class RWFJ {
    public static final String MODID = "rwfj";
    private static final Logger LOGGER = LogUtils.getLogger();

    public RWFJ(IEventBus modEventBus, ModContainer modContainer) {

    }
    
}
