package com.vfyjxf.rwfj;

import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.fml.ModContainer;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.loading.FMLEnvironment;
import org.slf4j.Logger;

import com.mojang.logging.LogUtils;


@Mod(value = RWFJ.MODID)
public class RWFJ {
    public static final String MODID = "rwfj";
    private static final Logger LOGGER = LogUtils.getLogger();

    public RWFJ(IEventBus modEventBus, ModContainer modContainer) {
        // Only initialize on client side
        if (FMLEnvironment.dist == Dist.CLIENT) {
            LOGGER.info("RainbowWrenchForJei loaded on client side");
        }
    }

}
