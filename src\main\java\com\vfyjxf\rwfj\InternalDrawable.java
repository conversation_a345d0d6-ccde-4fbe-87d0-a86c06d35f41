package com.vfyjxf.rwfj;

import com.mojang.math.Axis;
import mezz.jei.api.gui.ITickTimer;
import mezz.jei.api.gui.drawable.IDrawable;
import mezz.jei.common.util.TickTimer;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.resources.ResourceLocation;

import java.util.List;
import java.util.stream.Stream;

public class InternalDrawable implements IDrawable {

    private final int width;
    private final int height;
    private final ITickTimer tickTimer;
    private final List<ResourceLocation> resourceLocations = Stream.of(
            "textures/gui/config_button_cheat0.png",
            "textures/gui/config_button_cheat1.png",
            "textures/gui/config_button_cheat2.png",
            "textures/gui/config_button_cheat3.png",
            "textures/gui/config_button_cheat4.png",
            "textures/gui/config_button_cheat5.png",
            "textures/gui/config_button_cheat6.png",
            "textures/gui/config_button_cheat7.png"
    ).map(it -> ResourceLocation.fromNamespaceAndPath(RWFJ.MODID, it)).toList();

    public InternalDrawable(int width, int height) {
        this.width = width;
        this.height = height;
        this.tickTimer = new TickTimer(15, 7, false);
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public int getHeight() {
        return height;
    }
    
    @Override
    public void draw(GuiGraphics guiGraphics, int xOffset, int yOffset) {
        int index = tickTimer.getValue();
        int angle = 360 / resourceLocations.size();
        System.out.println("RWFJ InternalDrawable: Drawing at (" + xOffset + ", " + yOffset + ") with index " + index + " and angle " + (angle * index));

        var poseStack = guiGraphics.pose();
        poseStack.pushPose();
        poseStack.translate(xOffset + (float) width / 2, yOffset + (float) height / 2, 0);
        poseStack.mulPose(Axis.ZP.rotationDegrees(angle * index));
        try {
            // 尝试使用blitSprite方法（如果存在）
            guiGraphics.getClass().getMethod("blitSprite", ResourceLocation.class, int.class, int.class, int.class, int.class)
                    .invoke(guiGraphics, resourceLocations.get(index), -width/2, -height/2, width, height);
        } catch (Exception e) {
            // 如果blitSprite不存在，回退到blit方法
            System.out.println("RWFJ: blitSprite not available, using blit: " + e.getMessage());
            guiGraphics.blit(resourceLocations.get(index), -width/2, -height/2, 0, 0, width, height);
        }
        poseStack.popPose();
    }

}